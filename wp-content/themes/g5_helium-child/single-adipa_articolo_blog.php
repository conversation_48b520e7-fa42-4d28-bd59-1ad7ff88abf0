<?php
defined('ABSPATH') or die;

use Timber\Timber;

$gantry = Gantry\Framework\Gantry::instance();
$theme  = $gantry['theme'];

$context = Timber::get_context();
$context['page_head'] = $theme->render('partials/page_head.html.twig', $context);

$post = Timber::query_post();
$context['post'] = $post;
$context['wp_title'] .= ' - ' . $post->title();

$author = get_user_by('id', $post->post_author);

$post->acf = [
    'testo' => get_field('testo', $post->ID),
    'autore' => $author ? $author->display_name : '',
];

$galleria = [];
for ($i = 1; $i <= 6; $i++) {
    $foto = get_field("foto_{$i}", $post->ID);
    if ($foto) {
        $galleria[] = $foto;
    }
}
$post->acf['galleria'] = $galleria;

Timber::render(['single-' . $post->post_type . '.html.twig', 'single.html.twig'], $context);