<?php
/**
 * @package   Gantry 5 Theme
 * <AUTHOR> http://www.rockettheme.com
 * @copyright Copyright (C) 2007 - 2015 RocketTheme, LLC
 * @license   GNU/GPLv2 and later
 *
 * http://www.gnu.org/licenses/gpl-2.0.html
 */

defined('ABSPATH') or die;

use Timber\Timber;

$gantry = Gantry\Framework\Gantry::instance();
$theme  = $gantry['theme'];

// Inizializziamo il contesto
$context = Timber::get_context();
$context['page_head'] = $theme->render('partials/page_head.html.twig', $context);

// Impostiamo il titolo dell'archivio
$context['title'] = post_type_archive_title('', false);

// Specifichiamo i template da utilizzare, dando priorità al template specifico per eventi
$templates = ['archive-adipa_evento.html.twig', 'archive.html.twig'];

// Recuperiamo i post
$context['posts'] = Timber::get_posts();

// Aggiungiamo i campi ACF per ogni post
foreach($context['posts'] as $post) {
    // Recupera l'ID del termine da ACF per tipo_evento
    $tipo_evento_id = get_field('tipo_evento', $post->ID);
    // Recupera il termine usando l'ID per tipo_evento
    $tipo_evento_term = get_term($tipo_evento_id, 'tipo_evento');

    // Recupera i termini associati alla tassonomia 'area'
    $area_terms = get_the_terms($post->ID, 'area');
    $area_names = '';
    if ($area_terms && !is_wp_error($area_terms)) {
        $area_names = implode(', ', wp_list_pluck($area_terms, 'name'));
    }
    
    // Recupera l'autore del post
    $author = get_user_by('id', $post->post_author);
    
    $post->acf = [
        'tipo_evento' => $tipo_evento_term ? $tipo_evento_term->name : '',
        'area' => $area_names,
        'locandina' => get_field('locandina', $post->ID),
        'testo' => get_field('testo', $post->ID),
        'autore' => $author ? $author->display_name : ''
    ];
    
    // Recupero le immagini della galleria
    $galleria = [];
    for($i = 1; $i <= 6; $i++) {
        $foto = get_field("galleria_foto_{$i}", $post->ID);
        if($foto) {
            $galleria[] = $foto;
        }
    }
    $post->acf['galleria'] = $galleria;
}

Timber::render($templates, $context);
