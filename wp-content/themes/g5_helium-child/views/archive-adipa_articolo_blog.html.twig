{% extends "partials/page.html.twig" %}
{% set twigTemplate = 'archive-adipa_articolo_blog.html.twig' %}
{% set scope = 'archive-articoli-blog' %}

{% block content %}
    <div class="platform-content">
        <div class="archive-articoli-blog">
            <header class="page-header">
                <h1>{{ title }}</h1>
            </header>

            {% if posts is not empty %}
                <section class="entries">
                    <div class="g-grid">
                        {% for post in posts %}
                            <div class="g-block {% if pagination.current == 1 and 'sticky' in post.class %}size-100{% else %}{{ gantry.config.get('content.' ~ scope ~ '.contenuto.columns')|default('size-100') }}{% endif %}">
                                {% include 'partials/content-adipa_articolo_blog.html.twig' %}
                            </div>
                        {% endfor %}
                    </div>
                </section>

                {% if pagination.pages and pagination.pages|length > 1 %}
                    {% include 'partials/pagination.html.twig' %}
                {% endif %}

            {% else %}
                <div class="no-matches-notice">
                    <h1>Nessun articolo trovato</h1>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}