{% extends "partials/page.html.twig" %}
{% set twigTemplate = 'index.html.twig' %}
{% set scope = 'blog' %}

{% block content %}

    <div class="platform-content">
        <div class="blog">

            {# Begin Page Header #}
            {% if gantry.config.get('content.' ~ scope ~ '.heading.enabled', '0') and gantry.config.get('content.' ~ scope ~ '.heading.text') is not empty %}
                <header class="page-header">
                    <h1>
                        {{ gantry.config.get('content.' ~ scope ~ '.heading.text') }}
                    </h1>
                </header>
            {% endif %}
            {# End Page Header #}

            {# Begin Post Entries #}
            <section class="entries">
                <div class="g-grid">
                    {% for post in posts %}
                        <div class="g-block {% if pagination.current == 1 and 'sticky' in post.class %}size-100{% else %}{{ gantry.config.get('content.' ~ scope ~ '.content.columns')|default('size-100') }}{% endif %}">
                            {% include ['partials/content-' ~ scope ~ '.html.twig', (post.format) ? 'partials/content-' ~ post.format ~ '.html.twig' : '', 'partials/content.html.twig'] %}
                        </div>
                    {% endfor %}
                </div>
            </section>
            {# End Post Entries #}

            {# Begin Pagination #}
            {% if pagination.pages and pagination.pages|length > 1 %}
                {% include 'partials/pagination.html.twig' %}
            {% endif %}
            {# End Pagination #}

        </div>
    </div>

{% endblock %}
