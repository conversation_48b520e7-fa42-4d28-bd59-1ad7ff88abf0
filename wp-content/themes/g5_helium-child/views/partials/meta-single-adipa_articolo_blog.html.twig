<div class="entry-meta">

    {% block meta %}

        {# Begin Meta - Archive Link #}
        {% set archive_link = function('get_post_type_archive_link', 'adipa_articolo_blog') %}
        {% if archive_link %}
            <div class="meta-archive-link">
                <i class="fa fa-file-text-o" aria-hidden="true"></i>
                <span class="meta-prefix">Archivio: </span>
                <a href="{{ archive_link }}">Articoli Blog</a>
            </div>
        {% endif %}
        {# End Meta - Archive Link #}

        {# Begin Meta - Author #}
        {% if gantry.config.get('content.' ~ scope ~ '.meta-author.enabled', '1') and post.author.name %}
            <div class="meta-author">
                <i class="fa fa-pencil" aria-hidden="true"></i>

                {% if gantry.config.get('content.' ~ scope ~ '.meta-author.prefix', '') is not empty %}
                    <span class="meta-prefix">{{ gantry.config.get('content.' ~ scope ~ '.meta-author.prefix', '') ~ ' ' }}</span>
                {% endif %}

                <span class="author">{{ post.author.name }}</span>
            </div>
        {% endif %}
        {# End Meta - Author #}

        {# Begin Meta - Date #}
        {% if gantry.config.get('content.' ~ scope ~ '.meta-date.enabled', '1') and post.date %}
            <div class="meta-date">
                <i class="fa fa-clock-o" aria-hidden="true"></i>

                {% if gantry.config.get('content.' ~ scope ~ '.meta-date.prefix', '') is not empty %}
                    <span class="meta-prefix">{{ gantry.config.get('content.' ~ scope ~ '.meta-date.prefix', '') ~ ' ' }}</span>
                {% endif %}
                <span class="date">{{ post.date(gantry.config.get('content.' ~ scope ~ '.meta-date.format', 'j F Y')) }}</span>
            </div>
        {% endif %}
        {# End Meta - Date #}

    {% endblock %}

</div>