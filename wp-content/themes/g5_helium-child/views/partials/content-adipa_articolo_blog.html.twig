<article class="tease tease-{{ post.post_type }} {{ post.class }} clearfix" id="tease-{{ post.ID }}">

    {% block content %}

        <section class="entry-header">

            <h1 class="entry-title">
                <a href="{{ post.link }}" title="{{ post.title }}">{{ post.title }}</a>
            </h1>

            {% if gantry.config.get('content.' ~ scope ~ '.contenuto.visualizza-autore-sezione', '1') and post.acf.autore %}
                <div class="autore">
                    <strong>Autore:</strong>
                    {{ post.acf.autore|capitalize }}
                </div>
            {% endif %}
            
            {% if gantry.config.get('content.' ~ scope ~ '.contenuto.visualizza-area', '1') and post.acf.area %}
                <div class="autore">
                    <strong>Sezione:</strong>
                    {{ post.acf.area|capitalize }}
                </div>
            {% endif %}
        </section>

        <section class="entry-content">
            {% if post.acf.testo %}
                {% set testo_length = gantry.config.get('content.' ~ scope ~ '.contenuto.testo-length', '50')|int %}
                <div class="testo">
                    {{ post.acf.testo|split(' ')|slice(0, testo_length)|join(' ')|apply_filters('the_content')|raw }}
                </div>
            {% endif %}

            {% if gantry.config.get('content.' ~ scope ~ '.galleria.enabled', '1') and post.acf.galleria %}
                <div class="galleria-foto">
                    <h3>Galleria fotografica</h3>
                    <div class="gallery gallery-columns-3">
                        {% for foto in post.acf.galleria %}
                            <figure class="gallery-item">
                                <div class="gallery-icon landscape">
                                    <a href="{{ foto.url }}" target="_blank">
                                        <img src="{{ foto.sizes.medium }}" alt="{{ foto.alt }}">
                                    </a>
                                </div>
                            </figure>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </section>
    {% endblock %}

</article>