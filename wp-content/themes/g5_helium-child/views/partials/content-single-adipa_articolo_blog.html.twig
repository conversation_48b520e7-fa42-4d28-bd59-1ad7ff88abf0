<article class="post-type-{{ post.post_type }} {{ post.class }}" id="post-{{ post.ID }}">

    {% block content %}

        <section class="entry-header">

            {% if gantry.config.get('content.' ~ scope ~ '.title.enabled', '1') %}
                <h2 class="entry-title">
                    {% if gantry.config.get('content.' ~ scope ~ '.title.link', '0') %}
                        <a href="{{ post.link }}" title="{{ post.title }}">{{ post.title }}</a>
                    {% else %}
                        {{ post.title }}
                    {% endif %}
                </h2>
            {% endif %}

            {% if gantry.config.get('content.' ~ scope ~ '.meta-date.enabled', '1') or gantry.config.get('content.' ~ scope ~ '.meta-author.enabled', '1') or gantry.config.get('content.' ~ scope ~ '.meta-comments.enabled', '1') or gantry.config.get('content.' ~ scope ~ '.meta-categories.enabled', '1') or gantry.config.get('content.' ~ scope ~ '.meta-tags.enabled', '0') %}
                {% include ['partials/meta-single-adipa_articolo_blog.html.twig', 'partials/meta.html.twig'] %}
            {% endif %}

        </section>

        {% if not function('post_password_required', post.ID) %}

            <section class="entry-content">

                {% if post.acf.testo %}
                    <div class="acf-testo">
                        {{ post.acf.testo|raw }}
                    </div>
                {% endif %}

                {% if gantry.config.get('content.' ~ scope ~ '.galleria.enabled', '1') and post.acf.galleria %}
                    <div class="galleria-foto">
                        <h3>Galleria fotografica</h3>
                        <div class="gallery gallery-columns-3">
                            {% for foto in post.acf.galleria %}
                                <figure class="gallery-item">
                                    <div class="gallery-icon landscape">
                                        <a href="{{ foto.url }}" target="_blank">
                                            <img src="{{ foto.sizes.medium }}" alt="{{ foto.alt }}">
                                        </a>
                                    </div>
                                </figure>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

            </section>

        {% endif %}

    {% endblock %}

</article>