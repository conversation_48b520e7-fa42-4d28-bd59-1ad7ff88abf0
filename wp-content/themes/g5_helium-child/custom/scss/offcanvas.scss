/*- offcanvas -*/
$menuMobileBG-active:#777 ;
$menuMobileBG-hover:#777 ;
$menuMobileBG-open:#fff;
$offcanvasBG:#333;
$icona-mobile-menu-bg:transparent;
$icona-mobile-menu-open-bg:#fff;
$icona-mobile-menu-color:#333;
$icona-mobile-menu-open-color:grey;

/*----menu mobile offcanvas ---*/
#g-offcanvas {
  background: $offcanvasBG;
  #g-mobilemenu-container ul {
    background: $offcanvasMenuBG;
  }
  #g-mobilemenu-container {
    .g-toplevel li .g-menu-item-content
    {line-height:1.4em;}
  }

  #g-mobilemenu-container ul > li.g-menu-item-link-parent > .g-menu-item-container > .g-menu-parent-indicator {
    border: 0;
    background: none;
    border-radius: 0;
    margin: -0.3rem 0 -0.2rem 0.5rem;
    padding: 0.2rem;
  }
  #g-mobilemenu-container ul > li.g-menu-item-link-parent > .g-menu-item-container > .g-menu-parent-indicator:hover {
    border: 0;
    background: none;
    border-radius: 0;
    margin: -0.3rem 0 -0.2rem 0.5rem;
    padding: 0.2rem;
  }
  #g-mobilemenu-container ul > li:not(.g-menu-item-type-particle):not(.g-menu-item-type-module).active {
    background: $menuMobileBG-active;
  }
  #g-mobilemenu-container ul > li:not(.g-menu-item-type-particle):not(.g-menu-item-type-module):hover {
    background: $menuMobileBG-hover;
  }
}


//menu particle - top o footer -   pubblicato qui classe .menu-in-offcanvas messa in menu aggiunto su stile gantry in sezione offcanvas
.menu-in-offcanvas .g-content.g-particle {
  padding: 0;
  margin:0;
  a.g-menu-item-container  {
    font-size: 0.8rem;
    font-weight:300;
    display: block;
    padding:1.6em 1.6em 1.6em 1em;
    background-color: #515151;
    &:hover {
      text-decoration: underline;
      color:#ccc !important;
    }

  }

  .g-main-nav .g-toplevel > li {
    display:block;
    :hover {background-color:$menuMobileBG-hover;}
  }



}





/*---*/


div.g-offcanvas-toggle  {
  background-color:$icona-mobile-menu-bg;
  border-radius:0;
  width: inherit;
  height: inherit;
  top: 7px;
  padding-bottom: 2px;
  i.fa.fa-fw.fa-bars {
    color:$icona-mobile-menu-color;
  }
}

.g-offcanvas-open .g-offcanvas-toggle {
  background-color:$icona-mobile-menu-open-bg;
  i.fa.fa-fw.fa-bars {
    color:$icona-mobile-menu-open-color;
  }
}
