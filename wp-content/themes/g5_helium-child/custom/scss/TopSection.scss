$topsection:#ffffff;
$menuTopItemColor:#999;
$menuTopItemBG:transparent;
$menuTopItemHoverColor:#000;
$menuTopItemHoverBG:transparent;


ul#menu-top-menu  {margin:0; text-align: right;}



#g-topsection {
  .g-grid {
    height: 46px;
  }

  /*-------------cerca ---*/

  input[type="search"] {
    line-height: 1.5em;
  }
  .search-form input.search-field {
    color: #424753;
    margin-bottom: 0 !important;
  }
  .widget.widget_search input.search-field {
    margin-bottom: 10px;
    max-width: 200px;
    border-radius: $buttonRadius;
  }
  input[type="submit"] {
    border-radius: $buttonRadius;
    line-height: 1.5em;
  }

  input.search-submit.button
  {
    /*  padding: 0.7rem 2rem;
    margin-bottom: 0 !important;*/
    display:none;
  }

  /*---fine---*/

}

#g-topsection, .g-heliumTL .g-grid:first-child  {
  ul {}
  .g-content {
    display: inline-block;
  }

  .g-block {
    display: flex;
    flex-flow: column;
    justify-content: center;
  }
  .navbar-nav li {
    margin-right:1em;
  }
   .navbar-nav  li > a {
    font-size:0.9rem;
    color:$menuTopItemColor;
    background-color:$menuTopItemBG !important;
    margin-left:0.5em !important;
    border-radius: $buttonRadius;
  }

  .nav.menu.navbar-nav
  {
    margin-bottom: 0;
  }


  .navbar-nav li > a:hover{
    color:$menuTopItemHoverColor;
    background-color:$menuTopItemHoverBG !important;
  }
  .navbar-nav {
    padding: 0 29px;
    li {
      text-align:right !important;
      display:inline-block !important;

    }
  }
  .button  {
    padding: 0.9rem 2rem;
  }
}


/*---- cerca in g-topsection ---*/
#g-topsection, .g-heliumTL .g-grid:first-child {
  background-color:$topsection;
  p  {
    color:#ffffff;
    font-size:0.8em !important ;
  }
  .moduletable  ul.nav.menu.navbar-nav li {
    border-bottom:none;
  }
  form.form-inline {
    margin:0;
  }
  input.search-query {
    border-radius:0;
  }
  .search-query {
    float:left;
    height:35px;
    padding: 0px 0 0 20px;
    border-radius: 50px !important;
  }
}
