/*--------------Section Gantry Padding-------------*/

.mainPadding1 {padding:0px 13% !important;z-index:11;position:relative;}
.mainPadding2 {padding:10px 13%  !important;}
.mainPadding3 {padding:15px 13% !important;} /* usa per container */
.mainPadding4 { padding:30px 13% !important;} /* usa per footer */
.mainPadding5 {}



#g-headline .g-container{
	max-width: 100%;
	width: 100%;
	img {
		margin:0 auto;
		display: flex;
	}

}


.SubHeaderBG {
	background-color: aqua;
}
.highlightsSection {
	background-color: #efefef;
}

//--------------------------------
@media screen and (max-width:1400px) {/*portatili*/
	.mainPadding1 {padding:0px 7% !important;}
}

@media screen and (max-width:768px) {
	.mainPadding1   {padding:0px 0% !important;}

}

//------------------------------


@media screen and (max-width:1400px) {/*portatili*/
	.mainPadding2, .mainPadding3, .mainPadding4  {padding:10px 7% !important;}
}

@media screen and (max-width:768px) {
	.mainPadding2,.mainPadding3,.mainPadding4   {padding:10px 0% !important;}

}





/*--------------FINE Section Gantry Padding-------------*/





/* fine --------------------*/


#g-container-top .g-content {padding:0;margin:0;}
.g-wrapper  .g-content {
	padding:0.938rem 0.938rem 0.938rem 0.938rem;




}
@media screen and (min-width:1280px) {
	.g-wrapper  .g-content {
		padding:0px;
	}
}


@media screen and (max-width:400px) {
	.g-wrapper  .g-content {
		padding:0px;
	}
}

.bgcolored {
	background-color:#000;
}
.page-header {
	border-bottom: 1px solid #eee;
	margin-top: 0px !important;
	/*margin-bottom: 10px;
	padding-bottom: 10px;*/
	h1  {margin-bottom: 0;}
}
/*--- abovefooter  ---*/
.abovefooter {background-color:$AboveFooterBG;}
.abovefooter *{color:$AboveFooterColor;
	text-align:center;
}
/*--- footer section ----*/

.footerSection {
	line-height:1.1em;
	.g-content   {
		padding: 0.938rem;
	}
	a {
		font-weight:500;
		color:$footer-link !important;
		&:hover{
			font-weight:500;
			color:$footer-link-hover !important ;
		}
	}
}

/*---belowfooterSection---*/
.belowfooterSection {
	background-color:$BelowFooterSectionBG;
	* {
		color:$BelowFooterSectionColor;
	}
	a {
		font-weight:500;
		color:$BelowFooterSectionLink;
		&:hover {
			color:$BelowFooterSectionLinkHover
		}
	}
}

@media screen and (max-width:768px) {
	.belowfooterSection  {
		*	{
			text-align:center !important;
			margin:0 auto;
		}
		.g-content {
			width: 100%;
		}
	}
}

/*--------*/
