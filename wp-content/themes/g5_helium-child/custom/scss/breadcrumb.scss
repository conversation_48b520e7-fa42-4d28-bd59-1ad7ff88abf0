/*---- breadcrumb ---*/
/*--*/
$SubHeaderBG:transparent;
$breacrumbBG:#fff;
$breadcrumbColorHover:orange;
$breadcrumbColor:initial;
$breadcrumbColorActive:orange;
$altezzaBreadcrumb:12px;
/*--*/

$margine:($altezzaBreadcrumb/100)*40;

.breadcrumb {
	padding: 10px;
}
.moduletable  {
	ul.breadcrumb {
		background-color:$breacrumbBG;
		margin-bottom: 0;
		margin-top: 0;
		
		li {
			background-color:#ffffff;
			text-transform:uppercase;
			font-size:$altezzaBreadcrumb;
			margin-right: $margine;
			/*height:$altezzaBreadcrumb+$margine;*/
			.divider{
				line-height: $altezzaBreadcrumb*2;
			}
			&.active {
				span {
					color:$breadcrumbColorActive;
					font-weight:600;
					height:$altezzaBreadcrumb+$margine;
					margin-right:0;
				}
			}
			a  {
				color:$breadcrumbColor;
				span{
					&:hover {
					color:$breadcrumbColorHover;
					font-weight: 600;
				}
			}
		}
	}
}
}
