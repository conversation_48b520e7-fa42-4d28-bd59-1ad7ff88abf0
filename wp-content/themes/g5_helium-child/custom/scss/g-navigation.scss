// sass-lint:disable empty-line-between-blocks variable-name-format indentation property-sort-order nesting-depth no-ids
#g-navigation, #g-navigationTL {
	box-shadow: 0px 0px 20px #0004;
	background-color: $menuMainBG;
	.g-main-nav {
		.g-dropdown {
			border-radius: $MenuMainItemRadius;
			border: 0;
			background: $menuMainDropdownBG;
			@if $menuMainSubLevelshadow {
				box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
			}
		}
		.g-toplevel {

			line-height: inherit;
			> li {
				background-color: $menuMainItemBG;
				border-radius:$MenuMainItemRadius;
				margin: $menuMainItemDistance;
				padding: $menuMainItemPadding;
				.g-menu-item-title::after {
					content: " ";
					border: 2px solid #ffffff;
					position: absolute;
						bottom: -8px;
						left: 0px;
						margin-top: 0;
						border-width: 1px;
						border-style: solid;
						width: calc(0%);
						transition: width 0.3s ease-in-out;
				}
				
				.g-menu-item-title {
					color: #000;
					&:hover {
					
						&::after {
							content: " ";
							border: 2px solid #000;
							position: absolute;
							bottom: -8px;
							left: 0px;
							margin-top: 0;
							border-width: 1px;
							border-style: solid;
							width: calc(100%);
							
						}
					}
				}
				

					/*border-bottom: 2px solid $menuMainItemBGHover;*/

					> .g-menu-item-container {

						color:$menuMainItemColorHover;
					}
					.g-menu-parent-indicator {
						&::after {
							color:#000;
						}
					}
				
				.g-menu-parent-indicator {
					&::after {

						padding:0;
						height: auto;
					}
				}


				> .g-menu-item-container {
					color: $menuMainItemColor;
					font-weight: 600;
					//text-transform: uppercase;
					font-size: .9rem;
					padding: 0;

					> .g-menu-item-content {
						padding: 0 0 0;
					}
				}

				&.active {
					background-color: $menuMainItemBGActive;
					> .g-menu-item-container {
						> .g-menu-item-content {
							color:$menuMainItemColorActive;
							box-shadow: 0 0px 0px rgba(0, 0, 0,1 );
							/* qui la sottolineatura del toplevel */
							border:
							0px solid transparent {
								bottom:$menuMainItemUnderLine;
							}
						}
						&::after {
							content: " ";
							border: 2px solid #000;
							position: absolute;
							bottom: -8px;
							right: -8px;
							margin-top: 0;
							border-width: 1px;
							border-style: solid;
							width: calc(100% + 16px);
							
						}
					}
					a {
						border-bottom:0 !important;
						.g-menu-item-content {
							/* qui sotto attiva la riga sotto la voce selezionata nel sublevel */
							//border-bottom: $menuMainItemUnderLine;
							box-shadow: 0px;
						}
					}
				}
			}
		}



		.g-sublevel {
			margin:$menuMainSublevelMargin;
			.g-menu-item {
				padding: $menuMainSubItemPadding;
				border-bottom: $menuMainSubItemUnderLine;
			}

			> li {
				margin:0;
				color:violet;
				border-radius: $MenuMainItemRadius;
				margin: $menuMainSubItemMargin;
				.g-menu-parent-indicator::after {
					top: 0px;
					display: block;
					height: AUTO;
					position: relative;
				}
				&:not(:last-child) > .g-menu-item-container {
					border-bottom:0;// $menuMainSubItemUnderLine;
				}


				&.active {
					background-color: $menuMainSubItemBGActive;
					> .g-menu-item-container {
						color: $menuMainSubItemActive;
						font-weight: 600;
					}
				}
				&:hover {
					background-color: $menuMainSubItemBGHover;
					span {
						color: #000;
						// color: #ff3456;

					}
				}
				> .g-menu-item-container {
					font-weight: 300;
					//text-transform: uppercase;
					padding: 0;
					color:$menuMainSubItemColor;
				}
				.g-menu-item-title {
				/*color:$menuMainSubItemColor;*/
				}
				&.g-menu-item-type-particle {
					&:hover {
						background-color:transparent;
					}
				}
			}
			//terzo livello
			.g-sublevel {
				.g-menu-item-title  {
					//color:red;
					&:hover {
						/*color:green;*/
					}
					&:active {
					/*color: violet;*/
						font-weight: 600;
					}
				}

			}

		}

	}
}
