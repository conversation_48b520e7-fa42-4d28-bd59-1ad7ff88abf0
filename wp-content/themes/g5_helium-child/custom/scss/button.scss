
.wp-block-button {
	.wp-block-button__link{
		text-shadow:none;
		border-radius: $buttonRadius;
		padding: $paddingPulsanti;
		background-image: none;
		background: $colorePulsanti;
		border:1px solid transparent;
		line-height: 1em;
		color: $colorePulsantiLink;
		font-size: 1em;
		&:hover{
			border:1px solid transparent;
			background:	$coloreBGPulsantiHover;
			color:$coloreLinkPulsantiHover;
		}
	}

}

/*---------- button helium  ----*/
.button {
	background: $colorePulsanti;
	text-transform: inherit;
	border-radius: $buttonRadius;
}


/*----------- wp ------------*/

.post-content {
	a.read-more.button  {
		text-shadow:none;
		border-radius: $buttonRadius;
		color: #fff;
		padding: $paddingPulsanti;
		background-image: none;
		background: $colorePulsanti;
		font-size: $FontSize;

	}
}

.button {
	&:hover {
		background-color:$coloreBGPulsantiHover;
		color:$coloreLinkPulsantiHover;
		text-decoration:none;
	}
	&.button-xsmall {
		font-size: $FontSize;
		padding: 0.6rem 1.7rem;

	}
}



/*-----------------------*/
/*
p.readmore {
.btn  {
text-shadow:none;
border-radius: $buttonRadius;
color: #fff;
padding: $paddingPulsanti;
background-image: none;
background: $colorePulsanti;
&:hover {
background-color:$coloreBGPulsantiHover;
color:$coloreLinkPulsantiHover;
}
}

}
*/

.btn-primary {
	text-shadow:none;
	border-radius: $buttonRadius;
	padding: $paddingPulsanti;
	background-image: none;
	background: $colorePulsanti;
	border:1px solid transparent;
	&:hover{
		border:1px solid transparent;
	}
}
.controls {
	.btn-primary{
		text-shadow:none;
		border-radius: $buttonRadius;
		padding: $paddingPulsanti;
		background-image: none;
		background: $colorePulsanti;
	}
}


.btn-success:hover {

	background-color: $coloreBGPulsantiHover;
	background-image: none;
}
