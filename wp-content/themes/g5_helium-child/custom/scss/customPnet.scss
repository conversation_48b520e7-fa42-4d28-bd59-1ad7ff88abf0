@import 'dependencies';
@import 'variabili';
@import 'section';
@import 'breadcrumb';
@import 'g-navigation';
@import 'TopSection';
@import 'footer';
@import 'offcanvas';
@import 'highlight';
@import 'error';
@import 'search';
@import 'login';
@import 'lightgallery';
/*----------- wp --------------*/

.g-logo img {
  max-width: 220px;
  margin:  10px 0;
}
@media (max-width: 1200px) {
  .g-logo {
    padding-top:30px;
  }
}

.page {
  .g-content {
    margin-top:0;
    margin-bottom: 0;
    margin: 0;
  }
}

// Platform Content
#g-page-surround {
  .platform-content {

    // Entries
    .entries {
      margin-top:30px;
      /*border:3px solid red;*/
      .g-block {
        .tease {
          margin-bottom:2em;
          padding:0 0 2em 0;
          border-bottom:1px solid #ccc;
        }
      }
    }
  }
}


h2.entry-title {

  margin: 0.5em 0 1em 0;
  color:green;

}

/*--------------*/

/* definizione dimensione base testo per impostare rem */
html {
  .entry-content p {
    font-size:$FontSize !important;
    line-height:$LineHeightP !important;
  }
}
body {}

/*---plug social ---*/

div {
  .fastsocialshare_container {
    margin: 0 auto !important;
    display: table;
    -webkit-filter: grayscale(50%); /* Safari 6.0 - 9.0 */
    filter: grayscale(60%);
  }
}

/*--- modulo lingue -----*/
/*.mod-languages ul.lang-inline {padding-top: 0;}
ul.lang-inline img {border-radius:4px;}*/

/*---aggiungi a link per spostare il focus più in basso e non tagliare il contenuto---*/
/*
a.offsetAncora {
display: block;
position: relative;
top: -200px;
visibility: hidden;
}
*/

// centrare un contenuto in un div in verticale
.verticalAlign-top {
  display: flex;
  flex-flow: column;
  justify-content: flex-start;
}
.verticalAlign-middle {
  display: flex;
  flex-flow: column;
  justify-content: center;
}
.verticalAlign-bottom {
  display: flex;
  flex-flow: column;
  justify-content: flex-end;
}

// fine centrare un contenuto in un div in verticale

a.modal {
  position: relative;
  width: auto;
  display: inherit;
  color: #fff;
  background: $colorePulsanti;
}
strong {color:inherit;}
p {margin-top:0;}
.pull-left {
  &.item-image {
    margin: 0 0 1.5rem 0;
  }
}
/*corregge bug helium*/
.pagination {
  display: initial;
}

/* ---------------- SubHeaderBG Breadcrumb --------------- */
.SubHeaderBG {background-color:$SubHeaderBG;}
/*=============== skin moduletable==================*/


/*--------------------------------------------------------------*/
/*----- focus input ---------*/

select:focus, textarea:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="date"]:focus, input[type="month"]:focus, input[type="time"]:focus, input[type="week"]:focus, input[type="number"]:focus, input[type="email"]:focus, input[type="url"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="color"]:focus, .uneditable-input:focus   {
  border-color:  rgba($inputtheme,$maxOpacity) !important;
  box-shadow: 0 0px 0px rgba(0, 0, 0, $maxOpacity) inset, 0 0 8px rgba($inputtheme, $maxOpacity) !important;
  outline: 0 none !important;
}
select:hover, textarea:hover, input[type="text"]:hover, input[type="password"]:hover, input[type="datetime"]:hover, input[type="datetime-local"]:hover, input[type="date"]:hover, input[type="month"]:hover, input[type="time"]:hover, input[type="week"]:hover, input[type="number"]:hover, input[type="email"]:hover, input[type="url"]:hover, input[type="search"]:hover, input[type="tel"]:hover, input[type="color"]:hover, .uneditable-input:hover  {
  border-color: rgba($inputtheme, $minOpacity) !important;
  box-shadow: 0 0px 0px rgba(0, 0, 0, $minOpacity) inset, 0 0 8px rgba($inputtheme,$minOpacity) !important;
}
/*----- focus input end ---------*/


/* fix bootstrap 2.3.2 con la 4 */
.collapse {
  all: inherit;
}

// viste:

#g-page-surround  {

  .entry-meta {
    margin-bottom: 1em;
    * {font-size: 10px;}
  }
  @media (max-width: 1200px) {
    .entry-meta > div[class^="meta-"] {
      display:block;
    }
  }
}

.platform-content {
  /*------------------ lista articoli  in categoriia */
  .archive .entries {
    .post {
      /*  border:1px solid;*/
      background-color: #f7f2f2;
    }
    .entry-header,
    .post-excerpt
    {
      padding:1em 1.5em 0 1.5em;
    }
    .entry-header h1.entry-title,
    .blog .entry-header h1.entry-title
    {
      line-height: 1.2em;
      font-size: 1.5em;
      font-weight: 600;
      min-height: 60px;
      margin-bottom: 0;
    }
    .read-more {
      margin:0 1.5em;
    }
  }
  /*------------------ lista blog */
  .blog {
    .post {
      /*  border:1px solid;*/
      background-color: #f7f2f2;
    }
    .entry-header h1.entry-title  {
      line-height: 1.2em;
      font-size: 1.5em;
      font-weight: 600;
      min-height: 60px;
      margin-bottom: 0;
    }
    .entry-header, .post-excerpt {
      padding:1em 1.5em 0 1.5em;
    }
    .read-more {
      margin:0 1.5em;
    }
  }
}




/*personalizzazioni widtget in container*/

#g-container-main  {

  .widget_categories,
  .widget_recent_entries,
  .widget_nav_menu
  {
    h3.widgettitle.g-title {
      border-bottom:$WidgetTitleBorder;
      background-color: $WidgetTitleBG;
      padding:$WidgetTitlePadding;
      margin:$WidgetTitleMargin;
      font-size: 1em;
      font-weight: 700;
    }
  }
  .widget_nav_menu {
    ul.menu {margin-top: 0;}
  }
  .widget_categories,
  .widget_recent_entries,
  .widget_nav_menu
  {
    li {
      background-color: $Widget-voci-menu-BG;
      border-bottom: 1px solid  $Widget-separatore-voci-menu;
      /*&:hover {background-color:$Widget-voci-menu-BG-hover; }*/
      a {
        padding:$WidgetMenuPadding;
        color:  $WidgetMenuColor;
        display: block;
        line-height: 1.2em;
        &:hover {color:$WidgetColorHover;}
        font-size: 1em;
        &:hover {background-color:$Widget-voci-menu-BG-hover;

        }
      }
      ul.sub-menu {
        margin-left: 0;
        li { /* sottovoci*/
          background-color: $Widget-voci-menu-BG;
          border-top: 1px solid  $Widget-separatore-voci-menu;
          border-bottom: none;
          &:hover {background-color:$Widget-voci-menu-BG-hover; }
          a {
            padding:5px 0px 5px 1.5rem;
            color:  $WidgetMenuColor;
            display: block;
            line-height: 1.2em;

            &:hover {color:$WidgetColorHover;}
            font-size: 1em;
          }
        }
      }
    }
  }
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}