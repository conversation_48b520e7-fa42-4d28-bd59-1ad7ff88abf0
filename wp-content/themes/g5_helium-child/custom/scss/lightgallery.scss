#lightgallery.acf-galleria { // Selettore più specifico per la galleria iniziale
  display: flex; // Aggiungiamo flex per allineare le immagini
  flex-wrap: wrap; // Per mandare a capo le immagini se non ci stanno
  gap: 10px; // Spazio tra le immagini

  > .lg-thumb-item { // Applichiamo gli stili direttamente agli item della galleria
    border: 2px solid $colorePulsanti;
    border-radius: 4px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: inline-block;
    line-height: 0;
    width: 150px; // Definiamo una larghezza fissa per il contenitore
    height: 150px; // Definiamo un'altezza fissa per il contenitore

    img {
      display: block;
      width: 100%;
      height: 100%; // L'immagine occupa tutto lo spazio del contenitore
      object-fit: cover; // Fa sì che l'immagine copra lo spazio senza distorsioni
      object-position: center; // Centra l'immagine all'interno del box
    }

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

// Manteniamo gli stili per la lightbox se necessario,
// ma li separiamo per chiarezza
.lg-outer { // Questo è il selettore corretto per la lightbox aperta
  .lg-thumb-outer {
    margin-top: 10px;
    .lg-thumb-item {
      border: 2px solid $colorePulsanti;
      margin: 5px;
      border-radius: 4px;
      overflow: hidden;
    }
  }
}