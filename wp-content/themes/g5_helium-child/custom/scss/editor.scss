/* @import url("https://sitename.com/wp-content/themes/themename/style.css"); */

@import 'variabili';
@import 'button';
/*@import 'dependencies';*/



h1.entry-title {  border-bottom: 5px solid #000;
  padding-bottom: 0.5em;
  line-height: 1.2em;
}

h2,h3,h4,h5,h6 {
line-height:1.2em;
}


/*
.home {
h2   {
border-bottom: 0;
}
}*/

.uagb-heading-text {
  border-bottom: 0;

}

/*
* Main column width
* This changes it from the default 580px to 720px
*/
.wp-block {
  max-width: 720px;
}
.wp-block-column {
  &:not(:first-child) {
    margin-left: 16px;
  }
  figure {
    &.wp-block-image {
      margin:0;
    }
  }
}
/*
* Width of "wide" blocks
* This changes it from the default 1080px to 1280px
*/
.wp-block[data-align="wide"] {
  max-width: 1280px;
}
.widthAuto {
  width: auto !important;
  flex: inherit;
}


@media (min-width: 1400px) {
  .alignfull {
    width: 100vw !important;
    margin: 0 auto !important;
    left: calc(-50vw + 50%) !important;
    position: relative !important;
  }
  .alignwide {
    width: 1400px !important;
    margin: 0 auto !important ;
    //border:1px solid;
  }

}

/*------ personalizzazioni blocchi in edito guttemberg -------*/

@media (min-width: 1400px)  {

  .wp-block-image.alignfull,//da editor base
  .wp-block-cover.alignfull,//da pattern
  .wp-block-advgb-image.alignfull,
  .uagb-google-map__iframe
  {
    right:0 !important;
  }
}
.wp-block-cover.alignfull {margin-top: 50px !important;}
/*
* Width of "wide" blocks
* This changes it from the default 1080px to 1280px
*/
.wp-block[data-align="wide"] {
  max-width: 1280px;
}




/*---blocchi Ultimate addons block---*/

/*------classi utilità ----------------------------------------*/
@media screen and (max-width:768px) {
  .nascondi-mobile {
    display:none !important;
  }
}
@media screen and (max-width:1024px) {
  .nascondi-tablet {
    display:none !important;
  }
}

/*-------classi per gestire in responsivo i non margin e padding inseriti in
stile layout nelle section*/

@media screen and (max-width:768px) {
  .nomdarginall-phone, .nopaddingall-tablet {
    margin:inerith;
    padding:inerith;
  }
}
//-------------
