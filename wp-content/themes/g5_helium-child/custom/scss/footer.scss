$footerColor:#fff;
$footer-voci-menu:#979797;
$footer-voci-menuHover:#666;
$footer-link:#fff;
$footer-link-hover:#666;




/*------------------------footer  ---------------------------*/
#g-footer {

	.widgettitle {
		&.g-title {
			color:$footerColor ;
			font-size: 1em;
			font-weight: 700;
		}
	}
	p /*,span, .nav-header*/ {
		color:$footerColor ;
		font-size: 0.9em;
		font-weight: 300;
		line-height: 1.4em;
	}

	a {
		color:$footer-link;
		font-size: 0.9em;
		font-weight: 300;
		&:hover {
			text-decoration:none;
			color:$footer-link-hover;
		}
	}

	//--------------------------------------------------menu
	.menu > li {
		//	border-bottom: 1px solid #060606;
		padding: 0.2em 0.7em 0.2em 0em;
		ul li {padding: 0.5em 0 0 0;}

		ul {
			margin-left: 0.5rem;
		}


		a {
			font-size: 0.9em;
			color: $footer-voci-menu;
			&:hover {color:$footer-voci-menuHover;}
		}



	}

	/*--------- menu footer orizzontale  classe:footerMenu  in widget----------*/

	.footerMenu  {
		line-height: 0.8em;
		a:first-child {
			padding-bottom:1px;
			//	border-bottom:1px solid #8291a5 ;
			&:hover {
				text-decoration:none;
				border-bottom:mone ;
			}
		}
		.menu-main-menu-container ul#menu-main-menu.menu {margin-top: 0;}

		.sub-menu {
			/*max-width: 250px;*/
			margin-left: 10px;
		}
		ul#menu-main-menu.menu li ul.sub-menu li a {
			border-bottom:none ;
			font-size:0.8em ;
			line-height: 0.8em ;
			font-weight: 400 ;
		}
		ul.sub-menu li {
			display: block;
		}
		@media screen and (max-width:1200px) {
			ul#menu-main-menu.menu {
				display: block;
			}
		}
		ul:first-child {
			/*display: flex;
			display: -webkit-flex*/;
			display: inline-flex;
			justify-content:right;
			li {
				/*font-weight: bold;*/
				display: block;
				padding:10px 20px 2px 2px;
				text-align: initial;
				a {color:$footer-link;//text-transform: uppercase;
					font-weight: 700;
					font-size:0.9em;
				}
				a:hover { color:$footer-link-hover;}
			}
		}
	}
}
