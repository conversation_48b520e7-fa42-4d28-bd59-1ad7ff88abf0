<?php
/**
 * @package   Gantry 5 Theme
 * <AUTHOR> http://www.rockettheme.com
 * @copyright Copyright (C) 2007 - 2015 RocketTheme, LLC
 * @license   GNU/GPLv2 and later
 *
 * http://www.gnu.org/licenses/gpl-2.0.html
 */

defined('ABSPATH') or die;

use Timber\Timber;

/*
 * The Template for displaying all single posts
 */

$gantry = Gantry\Framework\Gantry::instance();
$theme  = $gantry['theme'];

// We need to render contents of <head> before plugin content gets added.
$context              = Timber::get_context();
$context['page_head'] = $theme->render('partials/page_head.html.twig', $context);

$post            = Timber::query_post();
$context['post'] = $post;
$context['wp_title'] .= ' - ' . $post->title();

// Aggiungiamo i campi ACF come in archive-adipa_evento.php
// Recupera il termine tipo_evento
$tipo_evento_id   = get_field('tipo_evento', $post->ID);
$tipo_evento_term = $tipo_evento_id ? get_term($tipo_evento_id, 'tipo_evento') : null;

// Recupera i termini associati alla tassonomia 'area'
$area_terms = get_the_terms($post->ID, 'area');
$area_names = '';
if ($area_terms && !is_wp_error($area_terms)) {
    $area_names = implode(', ', wp_list_pluck($area_terms, 'name'));
}

// Recupera l'autore del post
$author = get_user_by('id', $post->post_author);

// Costruiamo l'array acf
$post->acf = [
    'tipo_evento' => $tipo_evento_term ? $tipo_evento_term->name : '',
    'locandina'   => get_field('locandina', $post->ID),
    'area' => $area_names,
    'testo'       => get_field('testo', $post->ID),
    'autore'      => $author ? $author->display_name : '',
];

// Recupero le immagini della galleria
$galleria = [];
for ($i = 1; $i <= 6; $i++) {
    $foto = get_field("galleria_foto_{$i}", $post->ID);
    if ($foto) {
        $galleria[] = $foto;
    }
}
$post->acf['galleria'] = $galleria;

Timber::render(['single-' . $post->post_type . '.html.twig', 'single.html.twig'], $context);
