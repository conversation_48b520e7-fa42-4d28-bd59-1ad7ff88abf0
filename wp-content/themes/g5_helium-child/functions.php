<?php

/**
 * @package   Gantry 5 Theme
 * <AUTHOR> http://www.rockettheme.com
 * @copyright Copyright (C) 2007 - 2015 RocketTheme, LLC
 * @license   GNU/GPLv2 and later
 *
 * http://www.gnu.org/licenses/gpl-2.0.html
 */
function qot_add_editor_styles()
{
	add_editor_style('custom/css-compiled/editor_blank-helium.css');
}
add_action('after_setup_theme', 'qot_add_editor_styles');

add_action('after_setup_theme', 'misha_gutenberg_css');
function misha_gutenberg_css()
{
	add_theme_support('editor-styles');
	add_editor_style('custom/css-compiled/helium_blank-helium.css');
	add_editor_style('custom/css-compiled/editor_blank-helium.css');
	add_editor_style('custom/css-compiled/button_blank-helium.css');
}

function g5helium_child_setup()
{
	add_theme_support('align-wide');
}
add_action('after_setup_theme', 'g5helium_child_setup');

// Enqueue lightGallery assets
function child_enqueue_lightgallery() {
    // CSS e JS da CDN lightGallery
    wp_enqueue_style('lightgallery-css', 'https://cdn.jsdelivr.net/npm/lightgallery@2.4.0/css/lightgallery-bundle.min.css', array(), '2.4.0');
    wp_enqueue_script('lightgallery-js', 'https://cdn.jsdelivr.net/npm/lightgallery@2.4.0/lightgallery.umd.min.js', array(), '2.4.0', true);
    wp_enqueue_script('lg-thumbnail', 'https://cdn.jsdelivr.net/npm/lightgallery@2.4.0/plugins/thumbnail/lg-thumbnail.min.js', array('lightgallery-js'), '2.4.0', true);
    wp_enqueue_script('lg-fullscreen', 'https://cdn.jsdelivr.net/npm/lightgallery@2.4.0/plugins/fullscreen/lg-fullscreen.min.js', array('lightgallery-js'), '2.4.0', true);
}
add_action('wp_enqueue_scripts', 'child_enqueue_lightgallery');

// Remove all admin notices
remove_all_actions('admin_notices');
remove_all_actions('all_admin_notices');


/**
 * ============================================================
 *  ADIPA – automazione “Area” per CPT Eventi / Mostre / Articoli
 * ============================================================
 *
 * - ruolo custom: sezioni
 * - tassonomia:   area
 * - CPT coinvolti: eventi, mostre, articoli
 */

/*--------------------------------------------------------------
 # 1. Auto-assegna il termine "Area" al salvataggio del post
 --------------------------------------------------------------*/
add_action('save_post', 'adipa_autoset_area', 10, 3);
function adipa_autoset_area($post_id, $post, $update)
{

	/* Evita loop, salvataggi di revisioni o autosave */
	if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE)       return;
	if (wp_is_post_revision($post_id))                    return;

	/* Applica solo all'utente con ruolo sezioni                  */
	$user = wp_get_current_user();
	if (!in_array('sezioni', (array) $user->roles, true)) return;

	/* Applica solo ai CPT interessati                                   */
	$post_types = ['adipa_evento', 'adipa_mostra_scambio', 'adipa_articolo_blog'];
	if (! in_array($post->post_type, $post_types, true))   return;

	/* Recupera il termine "Area" abbinato all'utente                    */
	$area_term = (int) get_user_meta(get_current_user_id(), 'area_term_id', true);
	if (! $area_term) return;

	/* Assegna (o sostituisce) il termine Area al post                   */
	wp_set_post_terms($post_id, [$area_term], 'area', false);
}

/*--------------------------------------------------------------
 # 5. Aggiunge la colonna "Tipo Evento" nella lista admin di adipa_evento
 --------------------------------------------------------------*/
add_filter('manage_adipa_evento_posts_columns', function($columns) {
    $columns['tipo_evento'] = 'Tipo Evento';
    return $columns;
});

add_action('manage_adipa_evento_posts_custom_column', function($column, $post_id) {
    if ($column === 'tipo_evento') {
        if (function_exists('get_field')) {
            $tipo_evento = get_field('tipo_evento', $post_id);
            if ($tipo_evento) {
                if (is_array($tipo_evento)) {
                    // Se è un array (es. relazione o select multipla)
                    $names = [];
                    foreach ($tipo_evento as $term_id) {
                        $term = get_term($term_id);
                        if ($term && !is_wp_error($term)) {
                            $names[] = $term->name;
                        }
                    }
                    echo esc_html(join(', ', $names));
                } else {
                    $term = get_term($tipo_evento);
                    if ($term && !is_wp_error($term)) {
                        echo esc_html($term->name);
                    } else {
                        echo esc_html($tipo_evento);
                    }
                }
            } else {
                echo '-';
            }
        } else {
            echo '-';
        }
    }
}, 10, 2);
 
/*--------------------------------------------------------------
 # 4. Aggiunge la colonna "Area" nelle liste admin per i CPT interessati
 --------------------------------------------------------------*/
$post_types_with_area = ['adipa_evento', 'adipa_mostra_scambio', 'adipa_articolo_blog'];

foreach ($post_types_with_area as $pt) {
    add_filter("manage_{$pt}_posts_columns", function($columns) {
        $columns['area'] = 'Area';
        return $columns;
    });

    add_action("manage_{$pt}_posts_custom_column", function($column, $post_id) {
        if ($column === 'area') {
            $terms = get_the_terms($post_id, 'area');
            if (!empty($terms) && !is_wp_error($terms)) {
                $term_names = wp_list_pluck($terms, 'name');
                echo esc_html(join(', ', $term_names));
            } else {
                echo '-';
            }
        }
    }, 10, 2);
}

/*--------------------------------------------------------------
 # 2. Nascondi le metabox per i CPT agli utenti ruolo sezioni
 --------------------------------------------------------------*/
add_action('add_meta_boxes', 'adipa_hide_area_metabox', 99, 2);
function adipa_hide_area_metabox($post_type, $post)
{
	$user = wp_get_current_user();
	if (in_array('sezioni', (array) $user->roles, true)) {
		$targets = ['adipa_evento', 'adipa_mostra_scambio', 'adipa_articolo_blog'];
		if (in_array($post_type, $targets, true)) {
			/* Tassonomia gerarchica → ID {taxonomy}div */
			remove_meta_box('areadiv',       $post_type, 'side'); // gerarchica
			remove_meta_box('postimagediv',       $post_type, 'side'); // immagine in evidenza
			/* Per sicurezza rimuoviamo anche l'eventuale metabox non gerarchica */
			remove_meta_box('tagsdiv-area',  $post_type, 'side'); // non gerarchica (fallback)
			remove_meta_box('tagsdiv-tipo_evento',  $post_type, 'side'); // tassonomia tipo_evento
		}
	}
}


/*--------------------------------------------------------------
 # 3. Filtra la lista post in admin → mostra solo la propria area
 --------------------------------------------------------------*/
add_action('pre_get_posts', 'adipa_restrict_posts_to_area');
function adipa_restrict_posts_to_area($query)
{

	if (! is_admin() || ! $query->is_main_query()) return;
	$user = wp_get_current_user();
	if (! in_array('sezioni', (array) $user->roles, true)) return;

	$post_type = $query->get('post_type');
	$targets   = ['adipa_evento', 'adipa_mostra_scambio', 'adipa_articolo_blog'];
	if (! in_array($post_type, $targets, true))  return;

	$area_term = (int) get_user_meta(get_current_user_id(), 'area_term_id', true);
	if (! $area_term) return;

	$tax_filter = [
		[
			'taxonomy' => 'area',
			'field'    => 'term_id',
			'terms'    => [$area_term],
			'include_children' => false,
		],
	];
	$query->set('tax_query', $tax_filter);
}

/**
 * Aggiunge il campo "Area assegnata" nel profilo utente (solo per admin)
 */
add_action('show_user_profile',        'adipa_show_area_field');
add_action('edit_user_profile',        'adipa_show_area_field');
add_action('user_new_form',           'adipa_show_area_field'); // Add field to new user form
add_action('personal_options_update',  'adipa_save_area_field');
add_action('edit_user_profile_update', 'adipa_save_area_field');
add_action('user_register',           'adipa_save_area_field'); // Save field for new users

function adipa_show_area_field($user)
{
	if (! current_user_can('manage_options')) return; // solo admin

	// Recupera i termini della tassonomia "area"
	$aree = get_terms([
		'taxonomy'   => 'area',
		'hide_empty' => false,
	]);

	$selected = $user instanceof WP_User ? get_user_meta($user->ID, 'area_term_id', true) : '';
?>
	<h3>Area di competenza (Sezione)</h3>
	<table class="form-table">
		<tr>
			<th><label for="area_term_id">Area assegnata</label></th>
			<td>
				<select name="area_term_id" id="area_term_id">
					<option value="">— Nessuna —</option>
					<?php foreach ($aree as $term) : ?>
						<option value="<?php echo esc_attr($term->term_id); ?>" <?php selected($selected, $term->term_id); ?>>
							<?php echo esc_html($term->name); ?>
						</option>
					<?php endforeach; ?>
				</select>
				<p class="description">Seleziona l'area associata a questo utente (sezione).</p>
			</td>
		</tr>
	</table>
<?php
}

function adipa_save_area_field($user_id)
{
	if (! current_user_can('manage_options')) return;

	if (isset($_POST['area_term_id'])) {
		update_user_meta($user_id, 'area_term_id', intval($_POST['area_term_id']));
	}
}

/**
 * Disattiva Block Editor e rimuove editor classico
 * per CPT: eventi, mostre, articoli
 * Si usa solo ACF per inserire i dati
 */

/* 1. Disattiva Gutenberg */
add_filter('use_block_editor_for_post_type', function ($use_block_editor, $post_type) {
	$targets = ['adipa_evento', 'adipa_mostra_scambio', 'adipa_articolo_blog'];
	return in_array($post_type, $targets, true) ? false : $use_block_editor;
}, 10, 2);

/* 2. Rimuovi proprio il supporto "editor" (anche classic) */
add_action('init', function () {
	foreach (['adipa_evento', 'adipa_mostra_scambio', 'adipa_articolo_blog'] as $pt) {
		remove_post_type_support($pt, 'editor');
	}
}, 100);


/**
 * Aggiungi e popola la colonna personalizzata 'Area assegnata' nella tabella dell'elenco utenti di WordPress
 * 
 * Questo codice aggiunge una nuova colonna 'Area assegnata' alla schermata di gestione degli utenti di WordPress
 * e la popola con il nome del termine dell'area assegnata dai meta dati dell'utente.
 *
 * Il primo filtro aggiunge l'intestazione della colonna alla tabella dell'elenco utenti.
 * Il secondo filtro popola i dati della colonna per ogni utente tramite:
 * 1. Ottenimento dell'ID del termine dell'area dai meta dati dell'utente 'area_term_id'
 * 2. Recupero dell'oggetto termine corrispondente
 * 3. Visualizzazione del nome del termine o di un trattino se non è assegnata alcuna area
 *
 * @since 1.0.0
 * @uses manage_users_columns Filter hook to add column header
 * @uses manage_users_custom_column Filter hook to populate column data
 * @uses get_user_meta() To retrieve area term ID from user metadata
 * @uses get_term() To get the area term object by ID
 * @return mixed Column header name or column content
 */
/* Aggiungi la colonna */
add_filter('manage_users_columns', function ($columns) {
	$columns['area_ass'] = 'Area assegnata';
	return $columns;
});

/* Popola la colonna */
add_filter('manage_users_custom_column', function ($output, $column_name, $user_id) {
	if ('area_ass' === $column_name) {
		$term_id = get_user_meta($user_id, 'area_term_id', true);
		if (! $term_id) return '—';
		$term = get_term($term_id, 'area');
		return $term && ! is_wp_error($term) ? esc_html($term->name) : '—';
	}
	return $output;
}, 10, 3);

