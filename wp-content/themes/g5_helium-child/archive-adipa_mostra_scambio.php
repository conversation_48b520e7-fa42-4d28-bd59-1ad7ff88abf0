<?php
defined('ABSPATH') or die;

use Timber\Timber;

$gantry = Gantry\Framework\Gantry::instance();
$theme  = $gantry['theme'];

$context = Timber::get_context();
$context['page_head'] = $theme->render('partials/page_head.html.twig', $context);

$context['title'] = post_type_archive_title('', false);

$templates = ['archive-adipa_mostra_scambio.html.twig', 'archive.html.twig'];

$context['posts'] = Timber::get_posts();

foreach ($context['posts'] as $post) {
    // Recupera i termini associati alla tassonomia 'area'
    $area_terms = get_the_terms($post->ID, 'area');
    $area_names = '';
    if ($area_terms && !is_wp_error($area_terms)) {
        $area_names = implode(', ', wp_list_pluck($area_terms, 'name'));
    }

    $author = get_user_by('id', $post->post_author);

    $post->acf = [
        'area' => $area_names,
        'locandina' => get_field('locandina', $post->ID),
        'testo' => get_field('testo', $post->ID),
        'pdf' => get_field('pdf', $post->ID),
        'autore' => $author ? $author->display_name : '',
    ];

    $galleria = [];
    for ($i = 1; $i <= 6; $i++) {
        $foto = get_field("foto_{$i}", $post->ID);
        if ($foto) {
            $galleria[] = $foto;
        }
    }
    $post->acf['galleria'] = $galleria;
}

Timber::render($templates, $context);